#!/usr/bin/env python3
"""
EduGuideBot v3 Runner
Ready-to-use bot runner with Telegram token
"""

import asyncio
import logging
import os
import sys
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from bot.app import create_bot_application


def setup_logging():
    """Configure logging for the application."""
    logging.basicConfig(
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        level=logging.INFO,
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('eduguidebot.log')
        ]
    )

    # Reduce noise from some libraries
    logging.getLogger('httpx').setLevel(logging.WARNING)
    logging.getLogger('telegram').setLevel(logging.WARNING)


async def main():
    """Main entry point for EduGuideBot v3."""
    setup_logging()
    logger = logging.getLogger(__name__)

    # Use provided bot token
    bot_token = "8198268528:AAHuAvp3bRqO5hdPD3tbv1xSks2YjXtiu9Y"
    
    # You can also set it as environment variable if preferred
    # bot_token = os.getenv('BOT_TOKEN', "8198268528:AAHuAvp3bRqO5hdPD3tbv1xSks2YjXtiu9Y")

    logger.info("🚀 Starting EduGuideBot v3...")
    logger.info("📊 Features: 16-question assessment, MCDA+ML hybrid recommendations")
    logger.info("🏫 Data: 519 programs from 45 universities")
    logger.info("🗣️ Language: Full Khmer support")

    try:
        # Create and run the bot application
        application = create_bot_application(bot_token)
        
        logger.info("✅ Bot application created successfully")
        logger.info("🔗 Bot is ready to receive messages...")
        
        # Start polling for updates
        await application.run_polling(
            drop_pending_updates=True,
            close_loop=False
        )
        
    except KeyboardInterrupt:
        logger.info("🛑 EduGuideBot v3 stopped by user")
    except Exception as e:
        logger.error(f"💥 EduGuideBot v3 crashed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    print("🎓 EduGuideBot v3 - University Recommendation Bot")
    print("=" * 50)
    print("📱 Telegram Bot Token: Ready")
    print("🧠 MCDA + ML Hybrid Engine: Loaded")
    print("📚 University Database: 519 programs")
    print("🗣️ Language: Khmer + English")
    print("=" * 50)
    print("Starting bot... Press Ctrl+C to stop")
    print()
    
    asyncio.run(main())
