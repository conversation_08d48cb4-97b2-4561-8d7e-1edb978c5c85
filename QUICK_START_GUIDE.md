# 🎓 EduGuideBot v3 - Quick Start Guide

## ✅ Ready to Use Components

All components have been successfully built and tested:

### 1. 🧮 MCDA Scorer (`src/core/mcda/scorer.py`)
- **Real weight-based scoring** with 5 criteria
- Location (4.5), Budget (5.0), Interest Field (4.8), Career Goal (4.2), Learning Mode (3.8)
- Returns normalized scores (0-1)

### 2. 🤖 ML Model (`src/core/ml/model.py`)
- **Mock ML predictions** with realistic scoring
- Adjusts based on user preferences
- Returns top 5 matches with confidence scores

### 3. 🔄 Hybrid Recommendations (`src/bot/handlers/recommendations.py`)
- **MCDA (70%) + ML (30%)** blended scoring
- Uses real university data (519 programs from 45 universities)
- Generates top 5 personalized recommendations

### 4. 📱 Bot Handlers
- **Complete assessment flow** (16 questions)
- **Recommendation display** with action buttons
- **Major details** with full program information
- **University information** and contact details

### 5. 🧪 UX Simulator (`tools/ux_simulator.py`)
- **End-to-end testing** of complete user journey
- **Content validation** to ensure proper display
- **Error detection** for broken functionality

## 🚀 How to Run the Bot

### Option 1: Quick Start (Recommended)
```bash
python run_bot.py
```

### Option 2: With Environment Variable
```bash
export BOT_TOKEN="**********************************************"
python main.py
```

## 📱 Telegram Bot Token
**Ready to use:** `**********************************************`

## 🧪 Testing

### Run Complete Test Suite
```bash
python test_bot_complete.py
```

### Run Hybrid System Test
```bash
python test_hybrid_system.py
```

### Run UX Simulation
```bash
python tools/ux_simulator.py
```

## 📊 Test Results Summary

```
✅ Import Tests PASSED
✅ Data Loading PASSED (519 programs)
✅ MCDA Scoring PASSED
✅ ML Predictions PASSED
✅ Hybrid Recommendations PASSED
✅ Bot Creation PASSED

🎉 ALL TESTS PASSED!
```

## 🎯 Bot Features

### User Journey
1. **Language Selection** (Khmer/English)
2. **16-Question Assessment** (interests, location, budget, goals)
3. **Hybrid Recommendations** (top 5 personalized matches)
4. **Detailed Information** (program details, university info, contact)

### Recommendation System
- **MCDA Scoring:** Matches user preferences to program characteristics
- **ML Predictions:** Adjusts scores based on interest patterns
- **Hybrid Blend:** 70% MCDA + 30% ML for balanced recommendations

### Data Coverage
- **45 Universities** across Cambodia
- **519 Academic Programs** 
- **Full Khmer Language Support**
- **Comprehensive Program Details** (fees, duration, career prospects)

## 🔧 Architecture

```
EduGuideBot_v3/
├── src/
│   ├── core/
│   │   ├── mcda/scorer.py      # MCDA scoring engine
│   │   ├── ml/model.py         # ML prediction model
│   │   └── data_loader.py      # University data loader
│   ├── bot/
│   │   ├── handlers/           # Telegram bot handlers
│   │   ├── app.py             # Bot application setup
│   │   └── commands_v3.py     # Bot commands
│   └── tools/
│       └── ux_simulator.py    # UX testing simulator
├── data/raw/                  # University JSON files
├── run_bot.py                 # Ready-to-use bot runner
└── test_bot_complete.py       # Complete test suite
```

## 🎉 Ready for Production

The bot is fully functional and ready for deployment:

- ✅ **All imports fixed**
- ✅ **Hybrid recommendation system working**
- ✅ **Major details displaying correctly**
- ✅ **UX simulation validates real functionality**
- ✅ **Telegram token configured**
- ✅ **Error handling implemented**
- ✅ **Full Khmer language support**

## 🚀 Next Steps

1. **Start the bot:** `python run_bot.py`
2. **Test on Telegram:** Send `/start` to your bot
3. **Complete assessment:** Answer 16 questions
4. **View recommendations:** Get personalized university suggestions
5. **Explore details:** Click on recommendations for full information

**Bot Username:** Contact @BotFather to get your bot username
**Bot Token:** `**********************************************`
