#!/usr/bin/env python3
"""
EduGuideBot v3 Test Script
Quick test to verify bot structure and components
"""

import asyncio
import sys
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

async def test_bot_components():
    """Test that all bot components can be imported and initialized."""
    print("🧪 Testing EduGuideBot v3 Components...")
    
    try:
        # Test imports
        print("📦 Testing imports...")
        
        # Core components
        from src.core.mcda.scorer import calculate_mcda_score
        from src.core.ml.model import ml_recommender
        from src.core.recommender import get_recommendations
        from src.core.data.loader import load_university_data
        
        # Bot components
        from src.bot.app import create_bot_application
        from src.bot.commands_v3 import start_command, ai_status_command, ai_debug_command
        from src.bot.keyboards_v3 import create_language_keyboard, ASSESSMENT_QUESTIONS
        from src.bot.telegram_safe_v3 import safe_answer_callback, safe_edit_message
        
        # Handlers
        from src.bot.handlers.assessment import handle_language_selection, handle_assessment_answer
        from src.bot.handlers.recommendations import handle_recommendation_action, handle_major_details
        
        print("✅ All imports successful!")
        
        # Test data loading
        print("📊 Testing data loading...")
        university_data = await load_university_data()
        print(f"✅ Loaded {len(university_data)} university records")
        
        # Test ML model initialization
        print("🤖 Testing ML model...")
        ml_recommender.train_model([])  # Train with synthetic data
        print("✅ ML model initialized")
        
        # Test MCDA scoring
        print("📈 Testing MCDA scoring...")
        test_answers = {0: {'answer_index': 0, 'answer_text': 'test'}}
        test_major = {'city': 'Phnom Penh', 'tuition_fees_usd': 1000}
        score = calculate_mcda_score(test_answers, test_major)
        print(f"✅ MCDA score calculated: {score}")
        
        # Test recommendations
        print("🎯 Testing recommendations...")
        test_answers = {i: {'answer_index': 0, 'answer_text': 'test'} for i in range(16)}
        recommendations = await get_recommendations(test_answers, top_k=3)
        print(f"✅ Generated {len(recommendations)} recommendations")
        
        # Test keyboards
        print("⌨️ Testing keyboards...")
        lang_keyboard = create_language_keyboard()
        print(f"✅ Language keyboard created with {len(lang_keyboard.inline_keyboard)} rows")
        print(f"✅ Assessment has {len(ASSESSMENT_QUESTIONS)} questions")
        
        # Test UX simulator
        print("🎮 Testing UX simulator...")
        from tools.ux_simulator_v3 import run_ux_simulation
        sim_results = await run_ux_simulation()
        print(f"✅ UX simulation completed: {sim_results['passed']} passed, {sim_results['failed']} failed")
        
        print("\n🎉 All EduGuideBot v3 components working correctly!")
        print("\n📋 Summary:")
        print(f"   • University records: {len(university_data)}")
        print(f"   • Assessment questions: {len(ASSESSMENT_QUESTIONS)}")
        print(f"   • Sample recommendations: {len(recommendations)}")
        print(f"   • UX tests passed: {sim_results['passed']}/{sim_results['passed'] + sim_results['failed']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_bot_creation():
    """Test bot application creation."""
    print("\n🤖 Testing bot application creation...")
    
    try:
        from src.bot.app import create_bot_application
        
        # Create bot with dummy token
        app = create_bot_application("dummy_token")
        print("✅ Bot application created successfully")
        
        # Check handlers are registered
        handlers = app.handlers
        print(f"✅ {len(handlers)} handler groups registered")
        
        return True
        
    except Exception as e:
        print(f"❌ Bot creation failed: {e}")
        return False


if __name__ == "__main__":
    async def main():
        print("🚀 EduGuideBot v3 Component Test\n")
        
        # Test components
        components_ok = await test_bot_components()
        
        # Test bot creation
        bot_ok = await test_bot_creation()
        
        if components_ok and bot_ok:
            print("\n🎯 All tests passed! EduGuideBot v3 is ready to run.")
            print("\n📝 To start the bot:")
            print("   1. Set BOT_TOKEN environment variable")
            print("   2. Run: python main.py")
        else:
            print("\n❌ Some tests failed. Check the errors above.")
            sys.exit(1)
    
    asyncio.run(main())
