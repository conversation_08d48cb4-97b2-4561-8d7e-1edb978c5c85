#!/usr/bin/env python3
"""
Complete EduGuideBot v3 Test Suite
Tests all components including import fixes and recommendation system
"""

import sys
import asyncio
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

def test_imports():
    """Test that all imports work correctly."""
    print("🧪 Testing imports...")
    
    try:
        # Test core components
        from core.mcda.scorer import calculate_mcda_score
        from core.ml.model import ml_recommender
        from core.data_loader import load_raw
        print("   ✓ Core components imported")
        
        # Test bot handlers
        from bot.handlers.recommendations import hybrid_recommendation
        from bot.handlers.assessment import handle_language_selection
        from bot.handlers.details import show_major_details
        print("   ✓ Bot handlers imported")
        
        # Test bot app
        from bot.app import create_bot_application
        print("   ✓ Bot application imported")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ Import failed: {e}")
        return False


def test_data_loading():
    """Test university data loading."""
    print("📚 Testing data loading...")
    
    try:
        from core.data_loader import load_raw
        
        programs = load_raw()
        if not programs:
            print("   ❌ No programs loaded")
            return False
        
        print(f"   ✓ Loaded {len(programs)} programs")
        
        # Test program structure
        sample = programs[0]
        required_fields = ['major_name_en', 'university_name_en', 'city']
        
        for field in required_fields:
            if field not in sample:
                print(f"   ❌ Missing field: {field}")
                return False
        
        print("   ✓ Program structure validated")
        return True
        
    except Exception as e:
        print(f"   ❌ Data loading failed: {e}")
        return False


def test_mcda_scoring():
    """Test MCDA scoring system."""
    print("🧮 Testing MCDA scoring...")
    
    try:
        from core.mcda.scorer import calculate_mcda_score
        from core.data_loader import load_raw
        
        # Sample user profile
        user_answers = {
            0: {'answer_index': 2},  # Computer science
            2: {'answer_index': 0},  # Phnom Penh
            3: {'answer_index': 1}   # Medium budget
        }
        
        programs = load_raw()
        sample_program = programs[0]
        
        score = calculate_mcda_score(user_answers, sample_program)
        
        if not (0 <= score <= 1):
            print(f"   ❌ Invalid score range: {score}")
            return False
        
        print(f"   ✓ MCDA score: {score:.3f}")
        return True
        
    except Exception as e:
        print(f"   ❌ MCDA scoring failed: {e}")
        return False


def test_ml_predictions():
    """Test ML prediction system."""
    print("🤖 Testing ML predictions...")
    
    try:
        from core.ml.model import ml_recommender
        
        user_profile = {
            0: {'answer_index': 2},  # Computer science
            2: {'answer_index': 0},  # Phnom Penh
        }
        
        predictions = ml_recommender.predict(user_profile)
        
        if not predictions:
            print("   ❌ No predictions generated")
            return False
        
        if len(predictions) != 5:
            print(f"   ❌ Expected 5 predictions, got {len(predictions)}")
            return False
        
        # Validate prediction structure
        for pred in predictions:
            required_keys = ['name_en', 'university', 'score']
            for key in required_keys:
                if key not in pred:
                    print(f"   ❌ Missing prediction key: {key}")
                    return False
        
        print(f"   ✓ Generated {len(predictions)} ML predictions")
        return True
        
    except Exception as e:
        print(f"   ❌ ML predictions failed: {e}")
        return False


def test_hybrid_recommendations():
    """Test hybrid recommendation system."""
    print("🔄 Testing hybrid recommendations...")
    
    try:
        from bot.handlers.recommendations import hybrid_recommendation
        from core.data_loader import load_raw
        
        user_profile = {
            0: {'answer_index': 2},  # Computer science
            2: {'answer_index': 0},  # Phnom Penh
            3: {'answer_index': 1}   # Medium budget
        }
        
        programs = load_raw()
        recommendations = hybrid_recommendation(user_profile, programs)
        
        if not recommendations:
            print("   ❌ No recommendations generated")
            return False
        
        if len(recommendations) != 5:
            print(f"   ❌ Expected 5 recommendations, got {len(recommendations)}")
            return False
        
        # Validate recommendation structure
        for rec in recommendations:
            required_keys = ['program', 'hybrid_score', 'mcda_score', 'ml_score']
            for key in required_keys:
                if key not in rec:
                    print(f"   ❌ Missing recommendation key: {key}")
                    return False
        
        print(f"   ✓ Generated {len(recommendations)} hybrid recommendations")
        
        # Show top recommendation
        top_rec = recommendations[0]
        program = top_rec['program']
        name = program.get('major_name_en', 'Unknown')
        uni = program.get('university_name_en', 'Unknown')
        score = top_rec['hybrid_score']
        
        print(f"   ✓ Top recommendation: {name} at {uni} (Score: {score:.3f})")
        return True
        
    except Exception as e:
        print(f"   ❌ Hybrid recommendations failed: {e}")
        return False


async def test_bot_creation():
    """Test bot application creation."""
    print("🤖 Testing bot creation...")
    
    try:
        from bot.app import create_bot_application
        
        # Use test token
        test_token = "8198268528:AAHuAvp3bRqO5hdPD3tbv1xSks2YjXtiu9Y"
        
        application = create_bot_application(test_token)
        
        if not application:
            print("   ❌ Bot application not created")
            return False
        
        # Check handlers are registered
        handlers = application.handlers
        if not handlers:
            print("   ❌ No handlers registered")
            return False
        
        print(f"   ✓ Bot created with {len(handlers)} handler groups")
        return True
        
    except Exception as e:
        print(f"   ❌ Bot creation failed: {e}")
        return False


async def main():
    """Run complete test suite."""
    print("🎓 EduGuideBot v3 Complete Test Suite")
    print("=" * 50)
    
    tests = [
        ("Import Tests", test_imports),
        ("Data Loading", test_data_loading),
        ("MCDA Scoring", test_mcda_scoring),
        ("ML Predictions", test_ml_predictions),
        ("Hybrid Recommendations", test_hybrid_recommendations),
        ("Bot Creation", test_bot_creation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} CRASHED: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("✅ EduGuideBot v3 is ready for deployment")
        print("🚀 Run 'python run_bot.py' to start the bot")
        return True
    else:
        print(f"❌ {total - passed} tests failed")
        print("🔧 Fix the issues before deployment")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
