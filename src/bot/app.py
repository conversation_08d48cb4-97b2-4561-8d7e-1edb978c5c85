"""
EduGuideBot v3 Application
Main bot application setup and configuration.
"""

import logging
from telegram.ext import Application, CommandHandler, CallbackQueryHandler

from .commands_v3 import start_command, ai_status_command, ai_debug_command
from .handlers.assessment import handle_language_selection, handle_assessment_answer
from .handlers.recommendations import handle_recommendation_action, show_recommendations
from .handlers.details import show_major_details, show_university_info, handle_back_to_recommendations
from .telegram_safe_v3 import log_telegram_errors

logger = logging.getLogger(__name__)


def create_bot_application(token: str) -> Application:
    """Create and configure EduGuideBot v3 application."""
    # Create application
    application = Application.builder().token(token).build()

    # Add command handlers
    application.add_handler(CommandHandler("start", start_command))
    application.add_handler(CommandHandler("status", ai_status_command))
    application.add_handler(CommandHandler("debug", ai_debug_command))

    # Add callback query handlers
    application.add_handler(CallbackQueryHandler(handle_language_selection, pattern="^lang_"))
    application.add_handler(CallbackQueryHandler(handle_assessment_answer, pattern="^q\\d+_a\\d+$"))
    application.add_handler(CallbackQueryHandler(show_recommendations, pattern="^get_recommendations$"))
    application.add_handler(CallbackQueryHandler(handle_recommendation_action, pattern="^(details_|other_|location_|contact_)"))
    application.add_handler(CallbackQueryHandler(show_major_details, pattern="^major_\\d+$"))
    application.add_handler(CallbackQueryHandler(show_university_info, pattern="^(other_majors_|uni_location_|uni_contact_)"))
    application.add_handler(CallbackQueryHandler(handle_back_to_recommendations, pattern="^back_to_recommendations$"))
    application.add_handler(CallbackQueryHandler(handle_recommendation_action, pattern="^back_to_menu$"))

    # Add error handler
    application.add_error_handler(log_telegram_errors)

    logger.info("EduGuideBot v3 application configured successfully")
    return application



