"""
EduGuideBot v3 Telegram Safe Operations
Provides safe wrappers for Telegram operations with error handling
"""

import logging
import json
from pathlib import Path
from telegram import Update, CallbackQuery
from telegram.ext import ContextTypes
from telegram.error import BadRequest, TimedOut, NetworkError

logger = logging.getLogger(__name__)

# Create logs directory if it doesn't exist
logs_dir = Path(__file__).parents[2] / "tests" / "logs"
logs_dir.mkdir(parents=True, exist_ok=True)
failure_log_path = logs_dir / "failures.log"


def log_telegram_errors(func):
    """Decorator to log Telegram errors."""
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            logger.error(f"Telegram error in {func.__name__}: {e}")
            
            # Log to failure log
            error_data = {
                "function": func.__name__,
                "error": str(e),
                "error_type": type(e).__name__
            }
            
            with open(failure_log_path, "a", encoding="utf-8") as f:
                f.write(json.dumps(error_data, ensure_ascii=False) + "\n")
            
            # Return safe fallback message
            return "❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញ។ សូមព្យាយាមម្តងទៀត។"
    
    return wrapper


async def safe_answer_callback(query: CallbackQuery, text: str = None) -> bool:
    """Safely answer callback query with Khmer fallback."""
    try:
        if text:
            await query.answer(text)
        else:
            await query.answer()
        return True
    except Exception as e:
        logger.error(f"Error answering callback: {e}")
        try:
            await query.answer("❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញ។")
        except:
            pass
        return False


async def safe_edit_message(query: CallbackQuery, text: str, reply_markup=None, parse_mode=None) -> bool:
    """Safely edit message with Khmer fallback."""
    try:
        await query.edit_message_text(
            text=text,
            reply_markup=reply_markup,
            parse_mode=parse_mode
        )
        return True
    except BadRequest as e:
        if "message is not modified" in str(e).lower():
            logger.debug("Message not modified, skipping edit")
            return True
        logger.error(f"BadRequest editing message: {e}")
        return False
    except Exception as e:
        logger.error(f"Error editing message: {e}")
        try:
            await query.edit_message_text(
                "❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញ។ សូមព្យាយាមម្តងទៀត។"
            )
        except:
            pass
        return False


async def safe_send_message(update: Update, context: ContextTypes.DEFAULT_TYPE, text: str, reply_markup=None, parse_mode=None) -> bool:
    """Safely send message with Khmer fallback."""
    try:
        await update.message.reply_text(
            text=text,
            reply_markup=reply_markup,
            parse_mode=parse_mode
        )
        return True
    except Exception as e:
        logger.error(f"Error sending message: {e}")
        try:
            await update.message.reply_text(
                "❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញ។ សូមព្យាយាមម្តងទៀត។"
            )
        except:
            pass
        return False
