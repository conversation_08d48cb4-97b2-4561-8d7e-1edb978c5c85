"""
EduGuideBot v3 Keyboards
Creates inline keyboards for bot interactions
"""

from telegram import InlineKeyboardButton, InlineKeyboardMarkup

# 16 Assessment Questions with exact answers from specification
ASSESSMENT_QUESTIONS = [
    {
        "question": "តើអ្នកមានចំណាប់អារម្មណ៍ចំពោះមុខវិជ្ជាសិក្សាអ្វីខ្លះ?",
        "answers": ["គណិតវិទ្យា", "វិទ្យាសាស្ត្រ", "វិទ្យាសាស្ត្រកុំព្យូទ័រ", "អាជីពផ្នែកច្បាប់"]
    },
    {
        "question": "តើអ្នកចូលចិត្តធ្វើការតែម្នាក់ឯង ឬក៏ធ្វើការជាក្រុម?",
        "answers": ["តែម្នាក់ឯង", "ជាក្រុម", "មិនប្រាកដ", "មិនចង់ធ្វើអ្វីទេ"]
    },
    {
        "question": "តើអ្នកចង់ធ្វើការនៅទីក្រុងមួយណា?",
        "answers": ["ភ្នំពេញ", "សៀមរាប", "បាត់ដំបង", "ខេត្តផ្សេងទៀត"]
    },
    {
        "question": "តើអ្នកមានថវិកាសម្រាប់ចំណាយបង់ថ្លៃសិក្សាប៉ុន្មានក្នុងមួយឆ្នាំ?",
        "answers": ["តិចជាង $500", "$500–$1000", "$1000–$2000", "ច្រើនជាង $2000"]
    },
    {
        "question": "តើអ្នកមានបំណងបន្តការសិក្សាទៀតទេនៅពេលអនាគត?",
        "answers": ["បាទ", "ទេ", "មិនប្រាកដ", "មិនចាំបាច់"]
    },
    {
        "question": "តើអ្នកចូលចិត្តការងារដែលផ្ដល់ប្រាក់ខែខ្ពស់ ឬការងារដែលមានស្ថិរភាព?",
        "answers": ["ប្រាក់ខែខ្ពស់", "ស្ថិរភាព", "ច្នៃប្រឌិត", "ជួយអ្នកដទៃ"]
    },
    {
        "question": "តើអ្នកមានជំនាញខាងអ្វីខ្លះ?",
        "answers": ["គណិតវិទ្យា", "វិទ្យាសាស្ត្រ", "កុំព្យូទ័រ", "បច្ចេកវិទ្យា"]
    },
    {
        "question": "តើអ្នកមានចំណុចខ្សោយខាងអ្វីខ្លះ?",
        "answers": ["គណិតវិទ្យា", "ភាសាអង់គ្លេស", "កុំព្យូទ័រ", "ចំណាយពេលវេលា"]
    },
    {
        "question": "តើអ្នកចង់ធ្វើការនៅក្នុងប្រទេស ឬក្រៅប្រទេស?",
        "answers": ["ក្នុងប្រទេស", "ក្រៅប្រទេស", "មិនប្រាកដ", "មិនចាំបាច់"]
    },
    {
        "question": "តើអ្នកចូលចិត្តរៀនតាមប្រព័ន្ធអ្វី? (ឧ. រៀនផ្ទាល់ខ្លួន ឬរៀនតាមប្រព័ន្ធអនឡាញ)",
        "answers": ["រៀនផ្ទាល់ខ្លួន", "រៀនតាមអនឡាញ", "មិនប្រាកដ", "មិនចាំបាច់"]
    },
    {
        "question": "តើអ្នកចង់សិក្សានៅសាកលវិទ្យាល័យឯកជន ឬសាកលវិទ្យាល័យរដ្ឋ?",
        "answers": ["ឯកជន", "រដ្ឋ", "មិនប្រាកដ", "មិនចាំបាច់"]
    },
    {
        "question": "តើអ្នកមានជំនាញនិយាយភាសាអង់គ្លេសបានល្អទេ?",
        "answers": ["បានល្អ", "មធ្យម", "មិនបានល្អ", "មិនចាំបាច់"]
    },
    {
        "question": "តើអ្នកមានបទពិសោធន៍ ឬជំនាញពិសេសអ្វីខ្លះ?",
        "answers": ["បាន", "មិនបាន", "មានបទពិសោធន៍", "មិនបានទេ"]
    },
    {
        "question": "តើអ្នកមានផែនការអភិវឌ្ឍខ្លួនឯងយ៉ាងដូចម្តេចក្នុងរយៈពេលវែង?",
        "answers": ["បច្ចេកវិទ្យា", "ហិរញ្ញវត្ថុ", "អាជីព•ក្នុង•ស្រុក", "ការងារ•អន្តរជាតិ"]
    },
    {
        "question": "តើអ្នកចូលចិត្តធ្វើការងារផ្នែកណា?",
        "answers": ["វិស្វករ", "គ្រប់គ្រង", "គ្រូបង្រៀន", "អ្នកវិជ្ជាជីវៈ"]
    },
    {
        "question": "តើអ្នកមានផែនការអនាគតអ្វីខ្លះ?",
        "answers": ["បាទ", "ទេ", "មិនប្រាកដ", "មិនចាំបាច់"]
    }
]


def create_language_keyboard() -> InlineKeyboardMarkup:
    """Create language selection keyboard."""
    keyboard = [
        [
            InlineKeyboardButton("ខ្មែរ", callback_data="lang_kh"),
            InlineKeyboardButton("English", callback_data="lang_en")
        ]
    ]
    return InlineKeyboardMarkup(keyboard)


def create_assessment_keyboard(question_num: int) -> InlineKeyboardMarkup:
    """Create assessment question keyboard."""
    if question_num < 0 or question_num >= len(ASSESSMENT_QUESTIONS):
        return InlineKeyboardMarkup([])
    
    answers = ASSESSMENT_QUESTIONS[question_num]["answers"]
    keyboard = []
    
    for i, answer in enumerate(answers):
        keyboard.append([
            InlineKeyboardButton(
                answer,
                callback_data=f"ans_{question_num}_{i}"
            )
        ])
    
    return InlineKeyboardMarkup(keyboard)


def create_recommendation_keyboard(recommendations: list) -> InlineKeyboardMarkup:
    """Create recommendation action keyboard."""
    keyboard = []
    
    for i, rec in enumerate(recommendations[:5]):
        keyboard.append([
            InlineKeyboardButton(
                f"🔍 ព័ត៌មានបន្ថែម",
                callback_data=f"major_{rec.get('major_id', i)}"
            ),
            InlineKeyboardButton(
                f"🏫 មុខជំនាញផ្សេងទៀត",
                callback_data=f"other_{rec.get('university_id', i)}"
            )
        ])
        keyboard.append([
            InlineKeyboardButton(
                f"📍 ទីតាំងសាកលវិទ្យាល័យ",
                callback_data=f"location_{rec.get('university_id', i)}"
            ),
            InlineKeyboardButton(
                f"📞 ទំនាក់ទំនង",
                callback_data=f"contact_{rec.get('university_id', i)}"
            )
        ])
    
    keyboard.append([
        InlineKeyboardButton("🔙 ត្រឡប់ទៅបញ្ជី", callback_data="back_to_menu")
    ])
    
    return InlineKeyboardMarkup(keyboard)


def create_major_details_keyboard(major_id: str, university_id: str) -> InlineKeyboardMarkup:
    """Create major details action keyboard."""
    keyboard = [
        [
            InlineKeyboardButton("🏫 មុខជំនាញផ្សេងទៀត", callback_data=f"other_{university_id}"),
            InlineKeyboardButton("📍 ទីតាំងសាកលវិទ្យាល័យ", callback_data=f"location_{university_id}")
        ],
        [
            InlineKeyboardButton("📞 ទំនាក់ទំនង", callback_data=f"contact_{university_id}"),
            InlineKeyboardButton("🔙 ត្រឡប់ទៅបញ្ជី", callback_data="back_to_recommendations")
        ]
    ]
    return InlineKeyboardMarkup(keyboard)


def get_question_text(question_num: int) -> str:
    """Get question text by number."""
    if question_num < 0 or question_num >= len(ASSESSMENT_QUESTIONS):
        return "សំណួរមិនត្រឹមត្រូវ"
    return ASSESSMENT_QUESTIONS[question_num]["question"]


def get_answer_text(question_num: int, answer_index: int) -> str:
    """Get answer text by question number and answer index."""
    if question_num < 0 or question_num >= len(ASSESSMENT_QUESTIONS):
        return "មិនត្រឹមត្រូវ"
    
    answers = ASSESSMENT_QUESTIONS[question_num]["answers"]
    if answer_index < 0 or answer_index >= len(answers):
        return "មិនត្រឹមត្រូវ"
    
    return answers[answer_index]
