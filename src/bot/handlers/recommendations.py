"""
EduGuideBot v3 Recommendation Handlers
Handles recommendation actions and major details
"""

import logging
import sys
from pathlib import Path
from telegram import Update, CallbackQuery, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes

# Add project root to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent.parent))

from src.bot.keyboards_v3 import create_major_details_keyboard
from src.bot.telegram_safe_v3 import safe_answer_callback, safe_edit_message
from src.core.data_loader import load_raw
from src.core.mcda.scorer import calculate_mcda_score
from src.core.ml.model import ml_recommender

logger = logging.getLogger(__name__)

# Hybrid recommendation weights
HYBRID_WEIGHTS = {
    'mcda': 0.7,
    'ml': 0.3
}


def hybrid_recommendation(user_profile: dict, all_programs: list) -> list:
    """
    Generate hybrid recommendations using MCDA + ML scoring.

    Args:
        user_profile: User assessment answers
        all_programs: List of all available programs

    Returns:
        List of top 5 recommendations with hybrid scores
    """
    try:
        # Get ML predictions
        ml_predictions = ml_recommender.predict(user_profile)
        ml_scores = {p['name_en']: p['score'] * HYBRID_WEIGHTS['ml'] for p in ml_predictions}

        hybrid_scores = []

        # Calculate hybrid scores for all programs
        for program in all_programs[:100]:  # Limit to first 100 for performance
            try:
                # Calculate MCDA score
                mcda_score = calculate_mcda_score(user_profile, program) * HYBRID_WEIGHTS['mcda']

                # Get ML score (default to 0 if not found)
                program_name = program.get('major_name_en', '')
                ml_score = ml_scores.get(program_name, 0.3 * HYBRID_WEIGHTS['ml'])

                # Calculate hybrid score
                hybrid_score = mcda_score + ml_score

                hybrid_scores.append({
                    "program": program,
                    "hybrid_score": round(hybrid_score, 2),
                    "mcda_score": round(mcda_score, 2),
                    "ml_score": round(ml_score, 2)
                })

            except Exception as e:
                logger.error(f"Error scoring program {program.get('major_name_en', 'Unknown')}: {e}")
                continue

        # Sort by hybrid score and return top 5
        hybrid_scores.sort(key=lambda x: x["hybrid_score"], reverse=True)
        return hybrid_scores[:5]

    except Exception as e:
        logger.error(f"Error in hybrid recommendation: {e}")
        return []


async def show_recommendations(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Show top 5 recommendations to user."""
    query = update.callback_query
    await safe_answer_callback(query)

    try:
        user_profile = context.user_data
        all_programs = load_raw()

        if not all_programs:
            await safe_edit_message(
                query,
                "❌ សូមអភ័យទោស! មិនអាចផ្ទុកទិន្នន័យបាន។ សូមព្យាយាមម្តងទៀត។"
            )
            return

        # Generate recommendations
        recommendations = hybrid_recommendation(user_profile, all_programs)

        if not recommendations:
            await safe_edit_message(
                query,
                "❌ សូមអភ័យទោស! មិនអាចបង្កើតការណែនាំបាន។ សូមព្យាយាមម្តងទៀត។"
            )
            return

        # Store recommendations in context for later use
        context.user_data['recommendations'] = recommendations

        # Format recommendation text
        text = "🎯 ផ្អែកលើចម្លើយរបស់អ្នក នេះគឺជាការណែនាំល្អបំផុត:\n\n"

        for idx, rec in enumerate(recommendations, 1):
            program = rec["program"]
            name_kh = program.get("major_name_kh", program.get("major_name_en", "មិនមានឈ្មោះ"))
            uni_kh = program.get("university_name_kh", program.get("university_name_en", "មិនមានឈ្មោះ"))
            score_bar = "⭐" * max(1, int(rec["hybrid_score"] * 5))

            text += f"{idx}. {name_kh}\n"
            text += f"🏫 {uni_kh}\n"
            text += f"{score_bar} ({rec['hybrid_score'] * 100:.0f}% ត្រូវគ្នា)\n\n"

        # Create action buttons for each recommendation
        keyboard_buttons = []
        for i in range(min(5, len(recommendations))):
            keyboard_buttons.append([
                InlineKeyboardButton(f"🔍 ព័ត៌មានបន្ថែម #{i+1}", callback_data=f"details_{i}")
            ])

        keyboard_buttons.append([
            InlineKeyboardButton("🔙 ត្រឡប់ទៅបញ្ជី", callback_data="back_to_menu")
        ])

        keyboard = InlineKeyboardMarkup(keyboard_buttons)
        await safe_edit_message(query, text, keyboard)

    except Exception as e:
        logger.error(f"Error in show_recommendations: {e}")
        await safe_edit_message(
            query,
            "❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញ។ សូមព្យាយាមម្តងទៀត។"
        )


async def handle_recommendation_action(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle recommendation action callbacks."""
    query = update.callback_query
    await safe_answer_callback(query)

    try:
        callback_data = query.data

        if callback_data == "back_to_menu":
            # Return to main menu
            welcome_text = (
                "🎓 សូមស្វាគមន៍មកកាន់ EduGuideBot v3!\n\n"
                "ខ្ញុំនឹងជួយអ្នកស្វែងរកសាកលវិទ្យាល័យ និងមុខជំនាញដែលសមស្របសម្រាប់អ្នក។\n\n"
                "សូមជ្រើសរើសភាសា:"
            )

            from ..keyboards_v3 import create_language_keyboard
            keyboard = create_language_keyboard()

            await safe_edit_message(query, welcome_text, keyboard)

        elif callback_data.startswith("details_"):
            # Show major details
            detail_index = int(callback_data.split("_")[1])
            await show_recommendation_details(query, context, detail_index)

        elif callback_data.startswith("major_"):
            # Show major details
            major_id = callback_data.split("_")[1]
            await show_major_details(query, major_id)

        elif callback_data.startswith("other_"):
            # Show other majors from same university
            university_id = callback_data.split("_")[1]
            await show_other_majors(query, university_id)

        elif callback_data.startswith("location_"):
            # Show university location
            university_id = callback_data.split("_")[1]
            await show_university_location(query, university_id)

        elif callback_data.startswith("contact_"):
            # Show contact information
            university_id = callback_data.split("_")[1]
            await show_contact_info(query, university_id)

    except Exception as e:
        logger.error(f"Error in handle_recommendation_action: {e}")
        await safe_edit_message(
            query,
            "❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញ។ សូមព្យាយាមម្តងទៀត។"
        )


async def show_recommendation_details(query: CallbackQuery, context: ContextTypes.DEFAULT_TYPE, detail_index: int) -> None:
    """Show detailed information about a recommended major."""
    try:
        recommendations = context.user_data.get('recommendations', [])

        if detail_index >= len(recommendations):
            await safe_edit_message(
                query,
                "❌ រកមិនឃើញព័ត៌មានអំពីមុខជំនាញនេះ។"
            )
            return

        recommendation = recommendations[detail_index]
        program = recommendation['program']

        # Format details
        name_kh = program.get('major_name_kh', program.get('major_name_en', 'មិនមានឈ្មោះ'))
        uni_kh = program.get('university_name_kh', program.get('university_name_en', 'មិនមានឈ្មោះ'))

        details_text = f"🎓 {name_kh}\n\n"
        details_text += f"🏫 សាកលវិទ្យាល័យ: {uni_kh}\n"
        details_text += f"📍 ទីតាំង: {program.get('city', 'មិនមានទីតាំង')}\n"

        # Duration
        duration = program.get('study_duration_kh', program.get('degree_level_kh', 'មិនបានបញ្ជាក់'))
        details_text += f"⏱️ រយៈពេល: {duration}\n"

        # Fees
        fees_usd = program.get('tuition_fees_usd', '')
        fees_khr = program.get('tuition_fees_khr', '')
        if fees_usd:
            details_text += f"💰 ថ្លៃសិក្សា: ${fees_usd}"
            if fees_khr:
                details_text += f" / {fees_khr} រៀល"
            details_text += "\n"

        # Employment rate
        employment_rate = program.get('employment_rate', '')
        if employment_rate:
            details_text += f"📊 អត្រាការងារ: {employment_rate}%\n"

        # Match scores
        details_text += f"\n🎯 ពិន្ទុត្រូវគ្នា:\n"
        details_text += f"• ពិន្ទុសរុប: {recommendation['hybrid_score'] * 100:.0f}%\n"
        details_text += f"• MCDA: {recommendation['mcda_score'] * 100:.0f}%\n"
        details_text += f"• ML: {recommendation['ml_score'] * 100:.0f}%\n\n"

        # Career prospects
        careers_kh = program.get('potential_careers_kh', [])
        if careers_kh:
            details_text += f"🎯 ការងារអនាគត:\n"
            for career in careers_kh[:3]:  # Show top 3
                details_text += f"• {career}\n"
            details_text += "\n"

        # Language of instruction
        languages = program.get('language_of_instruction', [])
        if languages:
            details_text += f"🗣️ ភាសាបង្រៀន: {', '.join(languages)}\n"

        # Create action buttons
        keyboard_buttons = [
            [InlineKeyboardButton("🏫 មុខជំនាញផ្សេងទៀត", callback_data=f"other_{program.get('university_id', '')}")],
            [InlineKeyboardButton("📍 ទីតាំងសាកលវិទ្យាល័យ", callback_data=f"location_{program.get('university_id', '')}")],
            [InlineKeyboardButton("📞 ទំនាក់ទំនង", callback_data=f"contact_{program.get('university_id', '')}")],
            [InlineKeyboardButton("🔙 ត្រឡប់ទៅបញ្ជី", callback_data="back_to_recommendations")]
        ]

        keyboard = InlineKeyboardMarkup(keyboard_buttons)
        await safe_edit_message(query, details_text, keyboard)

    except Exception as e:
        logger.error(f"Error in show_recommendation_details: {e}")
        await safe_edit_message(
            query,
            "❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញ។ សូមព្យាយាមម្តងទៀត។"
        )


async def handle_major_details(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle major details callback."""
    query = update.callback_query
    await safe_answer_callback(query)

    try:
        major_id = query.data.split("_")[1]
        await show_major_details(query, major_id)

    except Exception as e:
        logger.error(f"Error in handle_major_details: {e}")
        await safe_edit_message(
            query,
            "❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញ។ សូមព្យាយាមម្តងទៀត។"
        )


async def show_major_details(query: CallbackQuery, major_id: str) -> None:
    """Show detailed information about a major."""
    try:
        # Get major details
        major_details = await get_major_details(major_id)
        
        if not major_details:
            await safe_edit_message(
                query,
                "❌ រកមិនឃើញព័ត៌មានអំពីមុខជំនាញនេះ។"
            )
            return
        
        # Format details
        details_text = f"🎓 {major_details.get('major_name', 'មិនមានឈ្មោះ')}\n\n"
        details_text += f"🏫 {major_details.get('university_name', 'មិនមានឈ្មោះ')}\n"
        details_text += f"📍 {major_details.get('location', 'មិនមានទីតាំង')}\n"
        details_text += f"⏱️ រយៈពេល: {major_details.get('duration', 'N/A')} ឆ្នាំ\n"
        details_text += f"💰 ថ្លៃសិក្សា: {major_details.get('fees_usd', 'N/A')} USD/ឆ្នាំ\n"
        details_text += f"📊 អត្រាការងារ: {major_details.get('employment_rate', 'N/A')}%\n\n"
        
        if major_details.get('description'):
            details_text += f"📝 ការពិពណ៌នា:\n{major_details['description']}\n\n"
        
        if major_details.get('career_prospects'):
            details_text += f"🎯 ការងារអនាគត:\n{major_details['career_prospects']}\n\n"
        
        if major_details.get('internship_availability'):
            details_text += f"💼 កម្មសិក្សា: {major_details['internship_availability']}\n\n"
        
        if major_details.get('contact_info'):
            details_text += f"📞 ទំនាក់ទំនង: {major_details['contact_info']}"
        
        # Create action buttons
        keyboard = create_major_details_keyboard(
            major_id, 
            major_details.get('university_id', '')
        )
        
        await safe_edit_message(query, details_text, keyboard)
        
    except Exception as e:
        logger.error(f"Error in show_major_details: {e}")
        await safe_edit_message(
            query,
            "❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញ។ សូមព្យាយាមម្តងទៀត។"
        )


async def show_other_majors(query: CallbackQuery, university_id: str) -> None:
    """Show other majors from the same university."""
    try:
        # Get university details and majors
        university_details = await get_university_details(university_id)
        
        if not university_details:
            await safe_edit_message(
                query,
                "❌ រកមិនឃើញព័ត៌មានអំពីសាកលវិទ្យាល័យនេះ។"
            )
            return
        
        majors_text = f"🏫 មុខជំនាញនៅ {university_details.get('name', 'មិនមានឈ្មោះ')}\n\n"
        
        majors = university_details.get('majors', [])[:10]  # Show top 10
        for i, major in enumerate(majors, 1):
            majors_text += f"{i}. {major.get('name', 'មិនមានឈ្មោះ')}\n"
            majors_text += f"   💰 {major.get('fees_usd', 'N/A')} USD\n\n"
        
        # Back button
        from telegram import InlineKeyboardButton, InlineKeyboardMarkup
        keyboard = InlineKeyboardMarkup([
            [InlineKeyboardButton("🔙 ត្រឡប់ទៅបញ្ជី", callback_data="back_to_recommendations")]
        ])
        
        await safe_edit_message(query, majors_text, keyboard)
        
    except Exception as e:
        logger.error(f"Error in show_other_majors: {e}")
        await safe_edit_message(
            query,
            "❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញ។ សូមព្យាយាមម្តងទៀត។"
        )


async def show_university_location(query: CallbackQuery, university_id: str) -> None:
    """Show university location information."""
    try:
        university_details = await get_university_details(university_id)
        
        if not university_details:
            await safe_edit_message(
                query,
                "❌ រកមិនឃើញព័ត៌មានអំពីសាកលវិទ្យាល័យនេះ។"
            )
            return
        
        location_text = f"📍 ទីតាំង {university_details.get('name', 'មិនមានឈ្មោះ')}\n\n"
        location_text += f"🏢 អាសយដ្ឋាន: {university_details.get('address', 'មិនមានអាសយដ្ឋាន')}\n"
        location_text += f"🌍 ខេត្ត/ក្រុង: {university_details.get('city', 'មិនមានទីតាំង')}\n"
        
        if university_details.get('campus_info'):
            location_text += f"🏫 ព័ត៌មានបន្ថែម: {university_details['campus_info']}\n"
        
        # Back button
        from telegram import InlineKeyboardButton, InlineKeyboardMarkup
        keyboard = InlineKeyboardMarkup([
            [InlineKeyboardButton("🔙 ត្រឡប់ទៅបញ្ជី", callback_data="back_to_recommendations")]
        ])
        
        await safe_edit_message(query, location_text, keyboard)
        
    except Exception as e:
        logger.error(f"Error in show_university_location: {e}")
        await safe_edit_message(
            query,
            "❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញ។ សូមព្យាយាមម្តងទៀត។"
        )


async def show_contact_info(query: CallbackQuery, university_id: str) -> None:
    """Show university contact information."""
    try:
        university_details = await get_university_details(university_id)
        
        if not university_details:
            await safe_edit_message(
                query,
                "❌ រកមិនឃើញព័ត៌មានអំពីសាកលវិទ្យាល័យនេះ។"
            )
            return
        
        contact_text = f"📞 ទំនាក់ទំនង {university_details.get('name', 'មិនមានឈ្មោះ')}\n\n"
        
        if university_details.get('phone'):
            contact_text += f"📱 ទូរស័ព្ទ: {university_details['phone']}\n"
        
        if university_details.get('email'):
            contact_text += f"📧 អ៊ីមែល: {university_details['email']}\n"
        
        if university_details.get('website'):
            contact_text += f"🌐 គេហទំព័រ: {university_details['website']}\n"
        
        if university_details.get('facebook'):
            contact_text += f"📘 Facebook: {university_details['facebook']}\n"
        
        # Back button
        from telegram import InlineKeyboardButton, InlineKeyboardMarkup
        keyboard = InlineKeyboardMarkup([
            [InlineKeyboardButton("🔙 ត្រឡប់ទៅបញ្ជី", callback_data="back_to_recommendations")]
        ])
        
        await safe_edit_message(query, contact_text, keyboard)
        
    except Exception as e:
        logger.error(f"Error in show_contact_info: {e}")
        await safe_edit_message(
            query,
            "❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញ។ សូមព្យាយាមម្តងទៀត។"
        )
