"""
EduGuideBot v3 Assessment Handlers
Handles language selection and 16-question assessment flow
"""

import logging
import sys
from pathlib import Path
from telegram import Update, CallbackQuery, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes

# Add project root to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent.parent))

from src.bot.keyboards_v3 import create_assessment_keyboard, get_question_text, get_answer_text
from src.bot.telegram_safe_v3 import safe_answer_callback, safe_edit_message

logger = logging.getLogger(__name__)


async def handle_language_selection(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle language selection callback."""
    query = update.callback_query
    await safe_answer_callback(query)
    
    try:
        # Extract language from callback data
        lang = query.data.split("_")[1]  # lang_kh or lang_en
        context.user_data['language'] = lang
        
        # Start assessment
        assessment_text = (
            "🎯 ការវាយតម្លៃ\n\n"
            "ខ្ញុំនឹងសួរសំណួរ ១៦ ដើម្បីយល់ពីចំណង់ចំណូលចិត្ត និងគោលដៅរបស់អ្នក។\n\n"
            "តោះចាប់ផ្តើម!"
        )
        
        # Show first question
        question_text = get_question_text(0)
        keyboard = create_assessment_keyboard(0)
        
        full_text = f"{assessment_text}\n\n📝 សំណួរ ១/១៦:\n{question_text}"
        
        await safe_edit_message(query, full_text, keyboard)
        
        # Initialize assessment
        context.user_data['current_question'] = 0
        context.user_data['assessment_answers'] = {}
        
    except Exception as e:
        logger.error(f"Error in handle_language_selection: {e}")
        await safe_edit_message(
            query,
            "❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញ។ សូមព្យាយាមម្តងទៀត។"
        )


async def handle_assessment_answer(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle assessment answer callback."""
    query = update.callback_query
    await safe_answer_callback(query)
    
    try:
        # Parse callback data: ans_question_answer
        parts = query.data.split("_")
        question_num = int(parts[1])
        answer_index = int(parts[2])
        
        # Store answer
        answer_text = get_answer_text(question_num, answer_index)
        context.user_data['assessment_answers'][question_num] = {
            'answer_index': answer_index,
            'answer_text': answer_text
        }
        
        # Move to next question
        next_question = question_num + 1
        
        if next_question < 16:  # More questions
            question_text = get_question_text(next_question)
            keyboard = create_assessment_keyboard(next_question)
            
            progress_text = f"📝 សំណួរ {next_question + 1}/១៦:\n{question_text}"
            
            await safe_edit_message(query, progress_text, keyboard)
            context.user_data['current_question'] = next_question
            
        else:  # Assessment complete
            await complete_assessment(query, context)
            
    except Exception as e:
        logger.error(f"Error in handle_assessment_answer: {e}")
        await safe_edit_message(
            query,
            "❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញ។ សូមព្យាយាមម្តងទៀត។"
        )


async def complete_assessment(query: CallbackQuery, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Complete assessment and show recommendations."""
    try:
        # Show processing message
        await safe_edit_message(query, "⏳ កំពុងដំណើរការ...")

        # Get user answers
        answers = context.user_data.get('assessment_answers', {})

        # Import hybrid recommendation system
        from .recommendations import hybrid_recommendation
        from src.core.data_loader import load_raw

        # Load all programs
        all_programs = load_raw()
        if not all_programs:
            await safe_edit_message(
                query,
                "❌ សូមអភ័យទោស! មិនអាចផ្ទុកទិន្នន័យបាន។ សូមព្យាយាមម្តងទៀត។"
            )
            return

        # Get hybrid recommendations
        recommendations = hybrid_recommendation(answers, all_programs)

        if not recommendations:
            await safe_edit_message(
                query,
                "❌ សូមអភ័យទោស! រកមិនឃើញការណែនាំសម្រាប់អ្នក។ សូមព្យាយាមម្តងទៀត។"
            )
            return

        # Store recommendations in context
        context.user_data['recommendations'] = recommendations

        # Format recommendations
        rec_text = "🎯 ការណែនាំសម្រាប់អ្នក\n\n"
        rec_text += "ខាងក្រោមនេះជាមុខជំនាញដែលសមស្របបំផុតសម្រាប់អ្នក:\n\n"

        for i, rec in enumerate(recommendations[:5], 1):
            program = rec['program']
            name_kh = program.get('major_name_kh', program.get('major_name_en', 'មិនមានឈ្មោះ'))
            uni_kh = program.get('university_name_kh', program.get('university_name_en', 'មិនមានឈ្មោះ'))
            location = program.get('city', 'មិនមានទីតាំង')
            fees = program.get('tuition_fees_usd', 'N/A')

            confidence = get_confidence_stars(rec['hybrid_score'])
            rec_text += f"{i}. {name_kh}\n"
            rec_text += f"🏫 {uni_kh}\n"
            rec_text += f"📍 {location}\n"
            if fees != 'N/A':
                rec_text += f"💰 ${fees} USD\n"
            rec_text += f"⭐ {confidence} ({rec['hybrid_score'] * 100:.0f}% ត្រូវគ្នា)\n\n"

        # Create action buttons for each recommendation
        keyboard_buttons = []
        for i in range(min(5, len(recommendations))):
            keyboard_buttons.append([
                InlineKeyboardButton(f"🔍 ព័ត៌មានបន្ថែម #{i+1}", callback_data=f"details_{i}")
            ])

        keyboard_buttons.append([
            InlineKeyboardButton("🔄 ធ្វើតេស្តម្តងទៀត", callback_data="restart_assessment")
        ])

        from telegram import InlineKeyboardMarkup
        keyboard = InlineKeyboardMarkup(keyboard_buttons)

        await safe_edit_message(query, rec_text, keyboard)

        # Update stats
        from src.bot.commands_v3 import bot_stats
        bot_stats['total_assessments'] += 1

    except Exception as e:
        logger.error(f"Error in complete_assessment: {e}")
        await safe_edit_message(
            query,
            "❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញ។ សូមព្យាយាមម្តងទៀត។"
        )


def get_confidence_stars(score: float) -> str:
    """Convert score to star rating."""
    if score >= 0.8:
        return "★★★★★"
    elif score >= 0.6:
        return "★★★★☆"
    elif score >= 0.4:
        return "★★★☆☆"
    else:
        return "★★☆☆☆"
