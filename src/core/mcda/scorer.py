"""
EduGuideBot v3 MCDA Scorer
Multi-Criteria Decision Analysis scoring for university recommendations
"""

import logging
from typing import Dict, List, Any

logger = logging.getLogger(__name__)

# MCDA Weights for different criteria
MCDA_WEIGHTS = {
    'location': 4.5,
    'budget': 5.0,
    'interest_field': 4.8,
    'career_goal': 4.2,
    'learning_mode': 3.8
}

# Location mapping
LOCATION_MAP = {
    0: 'phnom_penh',    # ភ្នំពេញ
    1: 'siem_reap',     # សៀមរាប
    2: 'battambang',    # បាត់ដំបង
    3: 'other'          # ខេត្តផ្សេងទៀត
}

# Budget mapping (USD per year)
BUDGET_MAP = {
    0: (0, 500),        # តិចជាង $500
    1: (500, 1000),     # $500–$1000
    2: (1000, 2000),    # $1000–$2000
    3: (2000, 10000)    # ច្រើនជាង $2000
}

# Interest field mapping
INTEREST_FIELD_MAP = {
    0: 'mathematics',           # គណិតវិទ្យា
    1: 'science',              # វិទ្យាសាស្ត្រ
    2: 'computer_science',     # វិទ្យាសាស្ត្រកុំព្យូទ័រ
    3: 'law'                   # អាជីពផ្នែកច្បាប់
}

# Career goal mapping
CAREER_GOAL_MAP = {
    0: 'high_salary',      # ប្រាក់ខែខ្ពស់
    1: 'stability',        # ស្ថិរភាព
    2: 'creativity',       # ច្នៃប្រឌិត
    3: 'helping_others'    # ជួយអ្នកដទៃ
}


def calculate_mcda_score(user_answers: Dict[int, Dict], major_data: Dict[str, Any]) -> float:
    """
    Calculate MCDA score for a major based on user answers.

    Args:
        user_answers: Dictionary of user answers from assessment
        major_data: Dictionary containing major information

    Returns:
        float: MCDA score between 0 and 1
    """
    try:
        total_score = 0.0
        total_weight = sum(MCDA_WEIGHTS.values())

        # Location score
        location_score = calculate_location_score(user_answers, major_data)
        total_score += location_score * MCDA_WEIGHTS['location']

        # Budget score
        budget_score = calculate_budget_score(user_answers, major_data)
        total_score += budget_score * MCDA_WEIGHTS['budget']

        # Interest field score
        interest_score = calculate_interest_score(user_answers, major_data)
        total_score += interest_score * MCDA_WEIGHTS['interest_field']

        # Career goal score
        career_score = calculate_career_score(user_answers, major_data)
        total_score += career_score * MCDA_WEIGHTS['career_goal']

        # Learning mode score
        learning_score = calculate_learning_score(user_answers, major_data)
        total_score += learning_score * MCDA_WEIGHTS['learning_mode']

        # Normalize to 0-1 range
        normalized_score = total_score / total_weight
        return min(1.0, max(0.0, normalized_score))

    except Exception as e:
        logger.error(f"Error calculating MCDA score: {e}")
        return 0.0


def calculate_location_score(user_answers: Dict[int, Dict], major_data: Dict[str, Any]) -> float:
    """Calculate location preference score."""
    try:
        # Question 3: Location preference
        if 2 not in user_answers:  # Question index 2 (3rd question)
            return 0.5  # Default score

        user_location_index = user_answers[2]['answer_index']
        preferred_location = LOCATION_MAP.get(user_location_index, 'other')

        major_location = major_data.get('city', '').lower()

        # Perfect match
        if preferred_location == 'phnom_penh' and ('phnom penh' in major_location or 'pp' in major_location):
            return 1.0
        elif preferred_location == 'siem_reap' and ('siem reap' in major_location or 'sr' in major_location):
            return 1.0
        elif preferred_location == 'battambang' and ('battambang' in major_location or 'btb' in major_location):
            return 1.0
        elif preferred_location == 'other':
            return 0.8  # Flexible location preference

        return 0.2  # Location mismatch

    except Exception as e:
        logger.error(f"Error calculating location score: {e}")
        return 0.5


def calculate_budget_score(user_answers: Dict[int, Dict], major_data: Dict[str, Any]) -> float:
    """Calculate budget compatibility score."""
    try:
        # Question 4: Budget preference
        if 3 not in user_answers:  # Question index 3 (4th question)
            return 0.5  # Default score

        user_budget_index = user_answers[3]['answer_index']
        budget_range = BUDGET_MAP.get(user_budget_index, (0, 10000))

        major_fees = major_data.get('tuition_fees_usd', 0)
        if isinstance(major_fees, str):
            try:
                major_fees = float(major_fees.replace('$', '').replace(',', '').replace(' ', ''))
            except:
                major_fees = 1000  # Default assumption
        elif not major_fees:
            major_fees = 1000  # Default assumption

        # Check if major fees fall within user's budget range
        if budget_range[0] <= major_fees <= budget_range[1]:
            return 1.0
        elif major_fees < budget_range[0]:
            return 0.8  # Under budget is good
        else:
            # Over budget - penalize based on how much over
            over_ratio = major_fees / budget_range[1] if budget_range[1] > 0 else 1.0
            if over_ratio <= 1.5:
                return 0.4
            else:
                return 0.1

    except Exception as e:
        logger.error(f"Error calculating budget score: {e}")
        return 0.5


def calculate_interest_score(user_answers: Dict[int, Dict], major_data: Dict[str, Any]) -> float:
    """Calculate interest field compatibility score."""
    try:
        # Question 1: Interest field
        if 0 not in user_answers:  # Question index 0 (1st question)
            return 0.5  # Default score
        
        user_interest_index = user_answers[0]['answer_index']
        interest_field = INTEREST_FIELD_MAP.get(user_interest_index, 'general')
        
        major_name = major_data.get('major_name_en', '').lower()
        major_category = major_data.get('category', '').lower()
        
        # Field matching logic
        if interest_field == 'mathematics':
            if any(keyword in major_name for keyword in ['math', 'statistics', 'actuarial']):
                return 1.0
            elif any(keyword in major_name for keyword in ['engineering', 'physics', 'economics']):
                return 0.7
        
        elif interest_field == 'science':
            if any(keyword in major_name for keyword in ['biology', 'chemistry', 'physics', 'science']):
                return 1.0
            elif any(keyword in major_name for keyword in ['medicine', 'pharmacy', 'engineering']):
                return 0.8
        
        elif interest_field == 'computer_science':
            if any(keyword in major_name for keyword in ['computer', 'software', 'information technology', 'it']):
                return 1.0
            elif any(keyword in major_name for keyword in ['engineering', 'data', 'digital']):
                return 0.7
        
        elif interest_field == 'law':
            if any(keyword in major_name for keyword in ['law', 'legal', 'justice']):
                return 1.0
            elif any(keyword in major_name for keyword in ['business', 'management', 'administration']):
                return 0.6
        
        return 0.3  # Default for no clear match
        
    except Exception as e:
        logger.error(f"Error calculating interest score: {e}")
        return 0.5


def calculate_career_score(user_answers: Dict[int, Dict], major_data: Dict[str, Any]) -> float:
    """Calculate career goal compatibility score."""
    try:
        # Question 6: Career preference
        if 5 not in user_answers:  # Question index 5 (6th question)
            return 0.5  # Default score
        
        user_career_index = user_answers[5]['answer_index']
        career_goal = CAREER_GOAL_MAP.get(user_career_index, 'general')
        
        major_name = major_data.get('major_name_en', '').lower()
        employment_rate = major_data.get('employment_rate', 50)
        
        # Career matching logic
        if career_goal == 'high_salary':
            # Fields typically associated with high salaries
            if any(keyword in major_name for keyword in ['engineering', 'computer', 'finance', 'medicine']):
                return 1.0
            elif any(keyword in major_name for keyword in ['business', 'economics', 'law']):
                return 0.8
        
        elif career_goal == 'stability':
            # Government and stable sector jobs
            if any(keyword in major_name for keyword in ['education', 'public', 'administration']):
                return 1.0
            elif employment_rate > 80:
                return 0.9
            elif employment_rate > 60:
                return 0.7
        
        elif career_goal == 'creativity':
            if any(keyword in major_name for keyword in ['art', 'design', 'media', 'creative']):
                return 1.0
            elif any(keyword in major_name for keyword in ['architecture', 'marketing']):
                return 0.8
        
        elif career_goal == 'helping_others':
            if any(keyword in major_name for keyword in ['medicine', 'nursing', 'social', 'education']):
                return 1.0
            elif any(keyword in major_name for keyword in ['psychology', 'counseling']):
                return 0.9
        
        return 0.4  # Default score
        
    except Exception as e:
        logger.error(f"Error calculating career score: {e}")
        return 0.5


def calculate_learning_score(user_answers: Dict[int, Dict], major_data: Dict[str, Any]) -> float:
    """Calculate learning mode compatibility score."""
    try:
        # Question 10: Learning mode preference
        if 9 not in user_answers:  # Question index 9 (10th question)
            return 0.5  # Default score
        
        user_learning_index = user_answers[9]['answer_index']
        
        # Learning mode mapping
        learning_modes = {
            0: 'in_person',     # រៀនផ្ទាល់ខ្លួន
            1: 'online',        # រៀនតាមអនឡាញ
            2: 'flexible',      # មិនប្រាកដ
            3: 'flexible'       # មិនចាំបាច់
        }
        
        preferred_mode = learning_modes.get(user_learning_index, 'flexible')
        
        # Check if major supports preferred learning mode
        learning_options = major_data.get('learning_modes', [])
        
        if preferred_mode == 'flexible':
            return 0.8  # Flexible learners adapt well
        elif preferred_mode in learning_options:
            return 1.0
        else:
            return 0.3  # Mode not supported
        
    except Exception as e:
        logger.error(f"Error calculating learning score: {e}")
        return 0.5
