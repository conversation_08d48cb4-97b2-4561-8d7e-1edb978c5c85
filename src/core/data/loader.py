"""
EduGuideBot v3 Data Loader
Loads and processes university data from JSON files
"""

import json
import logging
from pathlib import Path
from typing import List, Dict, Any
import asyncio

logger = logging.getLogger(__name__)

# Cache for loaded data
_data_cache = None
_cache_timestamp = None


async def load_university_data() -> List[Dict[str, Any]]:
    """
    Load university data from JSON files.
    
    Returns:
        List of major dictionaries with university information
    """
    global _data_cache, _cache_timestamp
    
    try:
        # Check cache (valid for 5 minutes)
        import time
        current_time = time.time()
        if _data_cache and _cache_timestamp and (current_time - _cache_timestamp) < 300:
            return _data_cache
        
        # Get project root directory
        project_root = Path(__file__).parents[3]
        data_dir = project_root / "data" / "raw"
        
        if not data_dir.exists():
            logger.error(f"Data directory not found: {data_dir}")
            return []
        
        all_majors = []
        
        # Process each city directory
        for city_dir in data_dir.iterdir():
            if not city_dir.is_dir():
                continue
            
            city_name = city_dir.name
            logger.info(f"Processing city: {city_name}")
            
            # Process each university file in the city
            for json_file in city_dir.glob("*.json"):
                try:
                    majors = await load_university_file(json_file, city_name)
                    all_majors.extend(majors)
                except Exception as e:
                    logger.error(f"Error loading {json_file}: {e}")
                    continue
        
        # Cache the data
        _data_cache = all_majors
        _cache_timestamp = current_time
        
        logger.info(f"Loaded {len(all_majors)} majors from {len(list(data_dir.rglob('*.json')))} files")
        return all_majors
        
    except Exception as e:
        logger.error(f"Error loading university data: {e}")
        return []


async def load_university_file(file_path: Path, city_name: str) -> List[Dict[str, Any]]:
    """
    Load and process a single university JSON file.
    
    Args:
        file_path: Path to the JSON file
        city_name: Name of the city (PP, SR, BTB)
        
    Returns:
        List of processed major dictionaries
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Extract university information
        university_info = data.get('university', {})
        majors_data = data.get('majors', [])
        
        if not majors_data:
            logger.warning(f"No majors found in {file_path}")
            return []
        
        processed_majors = []
        
        for major in majors_data:
            try:
                processed_major = process_major_data(major, university_info, city_name)
                if processed_major:
                    processed_majors.append(processed_major)
            except Exception as e:
                logger.error(f"Error processing major in {file_path}: {e}")
                continue
        
        logger.debug(f"Processed {len(processed_majors)} majors from {file_path}")
        return processed_majors
        
    except Exception as e:
        logger.error(f"Error loading file {file_path}: {e}")
        return []


def process_major_data(major: Dict[str, Any], university: Dict[str, Any], city_name: str) -> Dict[str, Any]:
    """
    Process and standardize major data.
    
    Args:
        major: Raw major data from JSON
        university: University information
        city_name: City code (PP, SR, BTB)
        
    Returns:
        Processed major dictionary
    """
    try:
        # Map city codes to full names
        city_map = {
            'PP': 'Phnom Penh',
            'SR': 'Siem Reap',
            'BTB': 'Battambang'
        }
        
        city_full_name = city_map.get(city_name, city_name)
        
        # Generate unique ID
        university_name = university.get('name_en', university.get('name', ''))
        major_name = major.get('name_en', major.get('name', ''))
        major_id = f"{city_name}_{university_name}_{major_name}".replace(' ', '_').lower()
        
        # Extract and standardize fees
        fees_usd = extract_fees(major.get('tuition_fees', major.get('fees', '')))
        
        # Extract duration
        duration = extract_duration(major.get('duration', ''))
        
        # Extract employment rate
        employment_rate = extract_employment_rate(major.get('employment_rate', ''))
        
        processed_major = {
            # Identifiers
            'id': major_id,
            'university_id': f"{city_name}_{university_name}".replace(' ', '_').lower(),
            
            # Names (Khmer and English)
            'major_name_kh': major.get('name_kh', major.get('name', '')),
            'major_name_en': major.get('name_en', major.get('name', '')),
            'university_name_kh': university.get('name_kh', university.get('name', '')),
            'university_name_en': university.get('name_en', university.get('name', '')),
            
            # Location
            'city': city_full_name,
            'city_code': city_name,
            
            # Financial information
            'tuition_fees_usd': fees_usd,
            'tuition_fees_original': major.get('tuition_fees', major.get('fees', '')),
            
            # Academic information
            'duration_years': duration,
            'employment_rate': employment_rate,
            'category': major.get('category', ''),
            'degree_level': major.get('degree_level', major.get('level', '')),
            
            # Descriptions
            'description_kh': major.get('description_kh', ''),
            'description_en': major.get('description_en', major.get('description', '')),
            'career_prospects_kh': major.get('career_prospects_kh', ''),
            'career_prospects_en': major.get('career_prospects_en', ''),
            
            # Requirements and subjects
            'requirements': major.get('requirements', []),
            'subjects': major.get('subjects', []),
            'prerequisites': major.get('prerequisites', []),
            
            # Additional information
            'language_of_instruction': major.get('language_of_instruction', []),
            'learning_modes': major.get('learning_modes', ['in_person']),
            'internship_availability': major.get('internship_availability', ''),
            
            # University contact information
            'university_address': university.get('address', ''),
            'university_phone': university.get('phone', ''),
            'university_email': university.get('email', ''),
            'university_website': university.get('website', ''),
            'university_facebook': university.get('facebook', ''),
            'campus_info': university.get('campus_info', ''),
            
            # Raw data for reference
            'raw_major': major,
            'raw_university': university
        }
        
        return processed_major
        
    except Exception as e:
        logger.error(f"Error processing major data: {e}")
        return None


def extract_fees(fees_str: str) -> float:
    """Extract numerical fees from string."""
    try:
        if not fees_str:
            return 1000.0  # Default assumption
        
        # Remove common currency symbols and text
        fees_clean = str(fees_str).replace('$', '').replace('USD', '').replace(',', '').strip()
        
        # Extract numbers
        import re
        numbers = re.findall(r'\d+\.?\d*', fees_clean)
        
        if numbers:
            return float(numbers[0])
        
        return 1000.0  # Default
        
    except Exception as e:
        logger.debug(f"Error extracting fees from '{fees_str}': {e}")
        return 1000.0


def extract_duration(duration_str: str) -> int:
    """Extract duration in years from string."""
    try:
        if not duration_str:
            return 4  # Default
        
        # Extract numbers
        import re
        numbers = re.findall(r'\d+', str(duration_str))
        
        if numbers:
            duration = int(numbers[0])
            # Assume reasonable range
            if 1 <= duration <= 8:
                return duration
        
        return 4  # Default
        
    except Exception as e:
        logger.debug(f"Error extracting duration from '{duration_str}': {e}")
        return 4


def extract_employment_rate(rate_str: str) -> float:
    """Extract employment rate percentage from string."""
    try:
        if not rate_str:
            return 70.0  # Default assumption
        
        # Extract numbers
        import re
        numbers = re.findall(r'\d+\.?\d*', str(rate_str))
        
        if numbers:
            rate = float(numbers[0])
            # Ensure reasonable range
            if 0 <= rate <= 100:
                return rate
            elif rate > 100:
                return rate / 10  # Might be in wrong format
        
        return 70.0  # Default
        
    except Exception as e:
        logger.debug(f"Error extracting employment rate from '{rate_str}': {e}")
        return 70.0


async def get_major_details(major_id: str) -> Dict[str, Any]:
    """Get detailed information about a specific major."""
    try:
        university_data = await load_university_data()
        
        for major in university_data:
            if major.get('id') == major_id:
                return major
        
        logger.warning(f"Major not found: {major_id}")
        return None
        
    except Exception as e:
        logger.error(f"Error getting major details: {e}")
        return None


async def get_university_details(university_id: str) -> Dict[str, Any]:
    """Get detailed information about a specific university."""
    try:
        university_data = await load_university_data()
        
        # Find majors from this university
        university_majors = [
            major for major in university_data 
            if major.get('university_id') == university_id
        ]
        
        if not university_majors:
            logger.warning(f"University not found: {university_id}")
            return None
        
        # Use first major to get university info
        first_major = university_majors[0]
        
        return {
            'id': university_id,
            'name': first_major.get('university_name_kh', first_major.get('university_name_en', 'មិនមានឈ្មោះ')),
            'city': first_major.get('city', 'មិនមានទីតាំង'),
            'address': first_major.get('university_address', 'មិនមានអាសយដ្ឋាន'),
            'phone': first_major.get('university_phone', ''),
            'email': first_major.get('university_email', ''),
            'website': first_major.get('university_website', ''),
            'facebook': first_major.get('university_facebook', ''),
            'campus_info': first_major.get('campus_info', ''),
            'majors': [
                {
                    'id': major.get('id', ''),
                    'name': major.get('major_name_kh', major.get('major_name_en', 'មិនមានឈ្មោះ')),
                    'fees_usd': major.get('tuition_fees_usd', 'N/A')
                }
                for major in university_majors
            ]
        }
        
    except Exception as e:
        logger.error(f"Error getting university details: {e}")
        return None
