"""
EduGuideBot v3 ML Model
Mock ML model for university recommendations
"""

import logging
import random
from typing import Dict, List, Any

logger = logging.getLogger(__name__)

# Sample majors for mock predictions
SAMPLE_MAJORS = [
    {"name_kh": "វិស្វកម្មកុំព្យូទ័រ", "name_en": "Computer Engineering", "university": "CUS", "score": 0.92},
    {"name_kh": "វិទ្យាសាស្ត្រកុំព្យូទ័រ", "name_en": "Computer Science", "university": "RUPP", "score": 0.87},
    {"name_kh": "ហិរញ្ញវត្ថុ", "name_en": "Finance", "university": "APS", "score": 0.81},
    {"name_kh": "ច្បាប់", "name_en": "Law", "university": "Norton", "score": 0.75},
    {"name_kh": "បច្ចេកវិទ្យាព័ត៌មាន", "name_en": "Information Technology", "university": "ITC", "score": 0.71},
    {"name_kh": "គ្រប់គ្រងអាជីវកម្ម", "name_en": "Business Management", "university": "AUPP", "score": 0.68},
    {"name_kh": "វិស្វកម្មស៊ីវិល", "name_en": "Civil Engineering", "university": "ITC", "score": 0.65},
    {"name_kh": "គណនេយ្យ", "name_en": "Accounting", "university": "UC", "score": 0.62}
]

class UniversityRecommender:
    """Mock ML-based recommender for university majors."""

    def __init__(self):
        self.is_ready = True

    def predict(self, user_profile: Dict[int, Dict]) -> List[Dict[str, Any]]:
        """
        Simulate ML prediction based on user profile.
        Returns list of top 5 programs with scores.

        Args:
            user_profile: User assessment answers

        Returns:
            List of predicted majors with scores
        """
        try:
            # Simulate ML prediction logic based on user preferences
            predictions = []

            # Get user preferences
            interest_field = user_profile.get(0, {}).get('answer_index', 0)
            location_pref = user_profile.get(2, {}).get('answer_index', 0)
            budget_pref = user_profile.get(3, {}).get('answer_index', 1)

            # Adjust scores based on preferences
            for major in SAMPLE_MAJORS:
                base_score = major['score']
                adjusted_score = base_score

                # Interest field adjustment
                if interest_field == 2 and 'computer' in major['name_en'].lower():
                    adjusted_score += 0.1
                elif interest_field == 1 and 'business' in major['name_en'].lower():
                    adjusted_score += 0.1
                elif interest_field == 3 and 'law' in major['name_en'].lower():
                    adjusted_score += 0.1

                # Location preference (mock adjustment)
                if location_pref == 0:  # Phnom Penh preference
                    if major['university'] in ['RUPP', 'Norton', 'AUPP']:
                        adjusted_score += 0.05

                # Budget adjustment
                if budget_pref <= 1:  # Lower budget preference
                    if major['university'] in ['RUPP', 'ITC']:
                        adjusted_score += 0.05

                # Add some randomness to simulate ML uncertainty
                adjusted_score += random.uniform(-0.05, 0.05)
                adjusted_score = max(0.0, min(1.0, adjusted_score))

                predictions.append({
                    "name_kh": major['name_kh'],
                    "name_en": major['name_en'],
                    "university": major['university'],
                    "score": round(adjusted_score, 2)
                })

            # Sort by score and return top 5
            predictions.sort(key=lambda x: x['score'], reverse=True)
            return predictions[:5]

        except Exception as e:
            logger.error(f"Error in ML prediction: {e}")
            return SAMPLE_MAJORS[:5]

    def predict_score(self, user_answers: Dict[int, Dict], major_data: Dict[str, Any]) -> float:
        """
        Predict compatibility score for a specific major.

        Args:
            user_answers: User assessment answers
            major_data: Major information

        Returns:
            float: Predicted score between 0 and 1
        """
        try:
            # Simple scoring based on major name matching
            major_name = major_data.get('major_name_en', '').lower()
            interest_field = user_answers.get(0, {}).get('answer_index', 0)

            base_score = 0.5

            # Interest field matching
            if interest_field == 2 and 'computer' in major_name:
                base_score = 0.8
            elif interest_field == 1 and any(field in major_name for field in ['business', 'management']):
                base_score = 0.75
            elif interest_field == 3 and 'law' in major_name:
                base_score = 0.7
            elif interest_field == 0 and 'engineering' in major_name:
                base_score = 0.72

            # Add some randomness
            base_score += random.uniform(-0.1, 0.1)
            return max(0.0, min(1.0, base_score))

        except Exception as e:
            logger.error(f"Error predicting ML score: {e}")
            return 0.5


# Global ML recommender instance
ml_recommender = UniversityRecommender()
