# EduGuideBot v3 🎓

**University Recommendation System for Cambodian Students**

## 🎯 Overview

EduGuideBot v3 is a complete rebuild focused on **reality over features**. This version implements exactly what was specified - no more, no less.

### Core Mission
- **Never lie to users** - only show what actually works
- **Focus on reality** - delete fake features, build only what can be delivered
- **Suggest alternatives** instead of automatically agreeing

## 🏗️ Architecture

### Bot Flow (4 Steps)
1. **Language Selection**: ខ្មែរ or English
2. **16-Question Assessment**: Button-only UX, no typing
3. **Hybrid Recommendations**: MCDA (70%) + ML (30%) scoring
4. **Major Details**: Full information with action buttons

### Technical Stack
- **Framework**: python-telegram-bot
- **Recommendation**: Hybrid MCDA + RandomForest ML
- **Data**: JSON files from data/raw/
- **Language**: Khmer-first with English support
- **Error Handling**: Safe wrappers with Khmer fallback messages

## 📁 File Structure

```
EduGuideBot_v3/
├── src/
│   ├── bot/
│   │   ├── handlers/
│   │   │   ├── assessment.py      # 16-question flow
│   │   │   └── recommendations.py # Results & details
│   │   ├── keyboards_v3.py        # All keyboards & questions
│   │   ├── commands_v3.py         # /start, /ai:status, /ai:debug
│   │   ├── telegram_safe_v3.py    # Error handling
│   │   └── app.py                 # Bot application
│   ├── core/
│   │   ├── mcda/
│   │   │   └── scorer.py          # MCDA scoring logic
│   │   ├── ml/
│   │   │   └── model.py           # RandomForest model
│   │   ├── data/
│   │   │   └── loader.py          # Data loading
│   │   └── recommender.py         # Hybrid recommendations
│   └── utils/
├── tools/
│   └── ux_simulator_v3.py         # UX testing
├── tests/
│   └── logs/
│       └── failures.log           # Error logging
├── data/                          # Your university data
├── locales/                       # Translations
├── main.py                        # Entry point
└── test_bot_v3.py                 # Component tests
```

## 🎮 Features Implemented

### ✅ Core Features
- **16-Question Assessment** with exact Khmer questions from spec
- **Hybrid Recommendation Engine** (MCDA 70% + ML 30%)
- **Location Filtering** (Phnom Penh, Siem Reap, Battambang)
- **Major Details View** with full information
- **Button-Only UX** - no manual typing required
- **Error Handling** with Khmer fallback messages
- **Developer Tools** (/ai:status, /ai:debug)
- **UX Simulator** for automated testing

### ❌ Features NOT Included
- Essay Helper
- Live Chat
- Internship Finder
- Market Trends
- Mental Health Support
- Study Mode Advisor
- Social Sharing
- Gamification

## 🚀 Quick Start

### 1. Install Dependencies
```bash
pip install python-telegram-bot scikit-learn numpy
```

### 2. Set Environment Variable
```bash
export BOT_TOKEN="your_telegram_bot_token"
```

### 3. Test Components
```bash
python test_bot_v3.py
```

### 4. Run Bot
```bash
python main.py
```

## 🧪 Testing

### Component Test
```bash
python test_bot_v3.py
```

### UX Simulation
```bash
python tools/ux_simulator_v3.py
```

### Developer Commands
- `/ai:status` - Show bot status and metrics
- `/ai:debug` - Run UX simulator and show results

## 📊 Assessment Questions

The bot uses exactly 16 questions in Khmer with 4 answer options each:

1. Interest field (Math, Science, Computer Science, Law)
2. Work preference (Alone, Team, Unsure, Don't care)
3. Location preference (Phnom Penh, Siem Reap, Battambang, Other)
4. Budget range (<$500, $500-1000, $1000-2000, >$2000)
5. Future studies (Yes, No, Unsure, Don't care)
6. Career priority (High salary, Stability, Creativity, Helping others)
7. Strengths (Math, Science, Computer, Technology)
8. Weaknesses (Math, English, Computer, Time management)
9. Work location (Domestic, International, Unsure, Don't care)
10. Learning mode (In-person, Online, Unsure, Don't care)
11. University type (Private, Public, Unsure, Don't care)
12. English skills (Good, Average, Poor, Don't care)
13. Experience (Have, Don't have, Some experience, None)
14. Development plan (Technology, Finance, Local career, International)
15. Work field (Engineer, Management, Teacher, Professional)
16. Future plans (Yes, No, Unsure, Don't care)

## 🎯 Recommendation Engine

### MCDA Scoring (70% weight)
- **Location**: 25% - Match user city preference
- **Budget**: 30% - Fit within user budget range
- **Interest**: 20% - Match field of interest
- **Career**: 15% - Align with career goals
- **Learning**: 10% - Support preferred learning mode

### ML Scoring (30% weight)
- **RandomForest** with 150 estimators, max depth 10
- **Features**: 16 assessment answers + major characteristics
- **Training**: Synthetic data generation for initial model

### Final Score
```
Hybrid Score = (MCDA × 0.7) + (ML × 0.3)
```

## 🛡️ Error Handling

All Telegram operations are wrapped in try/catch blocks:
- **Safe callbacks**: `safe_answer_callback()`
- **Safe message editing**: `safe_edit_message()`
- **Fallback message**: "❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញ។ សូមព្យាយាមម្តងទៀត។"
- **Error logging**: All errors logged to `tests/logs/failures.log`

## 📈 Success Metrics

- ✅ All 16 questions work perfectly
- ✅ Every answer uses button-based input
- ✅ Top 5 matches returned with confidence scores
- ✅ Each match has action buttons (Details, Other majors, Location, Contact)
- ✅ Location filtering works (city-based)
- ✅ No raw error messages shown to users
- ✅ All Telegram operations safe
- ✅ Developer tools functional
- ✅ UX simulator runs in <10 seconds

## 🔧 Development

### Adding New Features
1. **Check specification** - only build what's explicitly approved
2. **Ask first** - if in doubt, ask before building
3. **Test thoroughly** - use UX simulator
4. **Handle errors** - wrap in safe operations

### Code Style
- **Khmer-first** - all user-facing text in Khmer
- **Button-only UX** - no manual text input
- **Safe operations** - wrap all Telegram calls
- **Error logging** - log but don't show raw errors

## 📞 Support

This is EduGuideBot v3 - built for reality, not promises.

**Core Principle**: We do what works, we don't do what lies. We are doing what reality makes us unique - we don't lie to people. Let's begin with our capacity and ability. What's fake, delete it. We will learn and try to have it in the future.
